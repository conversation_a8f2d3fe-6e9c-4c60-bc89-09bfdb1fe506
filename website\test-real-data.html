<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kontour Coin - Real Data Test</title>
    <link href="styles/main.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #8b5cf6;
            margin-bottom: 1rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-error { background-color: #ef4444; }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .data-card {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #8b5cf6;
        }
        
        .data-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }
        
        .data-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .refresh-btn {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
        
        .log-container {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid #374151;
        }
        
        .log-timestamp {
            color: #9ca3af;
            margin-right: 0.5rem;
        }
        
        .log-success { color: #10b981; }
        .log-warning { color: #f59e0b; }
        .log-error { color: #ef4444; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-container">
                <div class="nav-brand">
                    <span class="brand-icon">💎</span>
                    <span class="brand-text">Kontour Coin</span>
                </div>
                <div class="nav-menu">
                    <a href="index.html" class="nav-link">Home</a>
                    <a href="#" class="nav-link active">Real Data Test</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="test-container">
        <div class="test-header">
            <h1>🔬 Real Data Integration Test</h1>
            <p>Testing live cryptocurrency data feeds and API connections</p>
            <button class="refresh-btn" onclick="refreshAllData()">🔄 Refresh All Data</button>
        </div>

        <!-- API Status Section -->
        <div class="test-section">
            <h3>📡 API Connection Status</h3>
            <div class="data-grid">
                <div class="data-card">
                    <div class="data-label">CoinGecko API</div>
                    <div class="data-value">
                        <span class="status-indicator status-warning" id="coingecko-status"></span>
                        <span id="coingecko-text">Testing...</span>
                    </div>
                </div>
                <div class="data-card">
                    <div class="data-label">Binance API</div>
                    <div class="data-value">
                        <span class="status-indicator status-warning" id="binance-status"></span>
                        <span id="binance-text">Testing...</span>
                    </div>
                </div>
                <div class="data-card">
                    <div class="data-label">Data Cache</div>
                    <div class="data-value">
                        <span class="status-indicator status-warning" id="cache-status"></span>
                        <span id="cache-text">Initializing...</span>
                    </div>
                </div>
                <div class="data-card">
                    <div class="data-label">Last Update</div>
                    <div class="data-value last-updated">Never</div>
                </div>
            </div>
        </div>

        <!-- Live Market Data Section -->
        <div class="test-section">
            <h3>💹 Live Market Data</h3>
            <div class="data-grid">
                <div class="data-card" data-coin="bitcoin">
                    <div class="data-label">Bitcoin (BTC)</div>
                    <div class="data-value">
                        <div class="price">Loading...</div>
                        <div class="change" style="font-size: 0.875rem; margin-top: 0.25rem;">Loading...</div>
                    </div>
                </div>
                <div class="data-card" data-coin="ethereum">
                    <div class="data-label">Ethereum (ETH)</div>
                    <div class="data-value">
                        <div class="price">Loading...</div>
                        <div class="change" style="font-size: 0.875rem; margin-top: 0.25rem;">Loading...</div>
                    </div>
                </div>
                <div class="data-card" data-coin="solana">
                    <div class="data-label">Solana (SOL)</div>
                    <div class="data-value">
                        <div class="price">Loading...</div>
                        <div class="change" style="font-size: 0.875rem; margin-top: 0.25rem;">Loading...</div>
                    </div>
                </div>
                <div class="data-card" data-coin="cardano">
                    <div class="data-label">Cardano (ADA)</div>
                    <div class="data-value">
                        <div class="price">Loading...</div>
                        <div class="change" style="font-size: 0.875rem; margin-top: 0.25rem;">Loading...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Global Statistics Section -->
        <div class="test-section">
            <h3>🌍 Global Market Statistics</h3>
            <div class="data-grid">
                <div class="data-card">
                    <div class="data-label">Total Market Cap</div>
                    <div class="data-value total-market-cap">Loading...</div>
                </div>
                <div class="data-card">
                    <div class="data-label">24h Volume</div>
                    <div class="data-value total-volume">Loading...</div>
                </div>
                <div class="data-card">
                    <div class="data-label">Market Cap Change</div>
                    <div class="data-value market-cap-change">Loading...</div>
                </div>
                <div class="data-card">
                    <div class="data-label">Active Cryptocurrencies</div>
                    <div class="data-value active-cryptocurrencies">Loading...</div>
                </div>
            </div>
        </div>

        <!-- System Log Section -->
        <div class="test-section">
            <h3>📋 System Log</h3>
            <div class="log-container" id="system-log">
                <div class="log-entry">
                    <span class="log-timestamp">[00:00:00]</span>
                    <span class="log-success">System initialized</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="scripts/real-data-integration.js"></script>
    <script>
        // Test-specific functionality
        let testLog = [];
        
        function addLogEntry(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('system-log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            let typeClass = '';
            switch(type) {
                case 'success': typeClass = 'log-success'; break;
                case 'warning': typeClass = 'log-warning'; break;
                case 'error': typeClass = 'log-error'; break;
                default: typeClass = '';
            }
            
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="${typeClass}">${message}</span>
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Keep only last 50 entries
            const entries = logContainer.querySelectorAll('.log-entry');
            if (entries.length > 50) {
                entries[0].remove();
            }
        }
        
        function updateAPIStatus(api, status, message) {
            const statusElement = document.getElementById(`${api}-status`);
            const textElement = document.getElementById(`${api}-text`);
            
            if (statusElement && textElement) {
                statusElement.className = `status-indicator status-${status}`;
                textElement.textContent = message;
            }
        }
        
        async function testAPIs() {
            addLogEntry('Testing API connections...', 'info');
            
            // Test CoinGecko API
            try {
                const response = await fetch('https://api.coingecko.com/api/v3/ping');
                if (response.ok) {
                    updateAPIStatus('coingecko', 'success', 'Connected');
                    addLogEntry('CoinGecko API: Connected', 'success');
                } else {
                    throw new Error('API returned error status');
                }
            } catch (error) {
                updateAPIStatus('coingecko', 'error', 'Failed');
                addLogEntry(`CoinGecko API: Failed - ${error.message}`, 'error');
            }
            
            // Test Binance API
            try {
                const response = await fetch('https://api.binance.com/api/v3/ping');
                if (response.ok) {
                    updateAPIStatus('binance', 'success', 'Connected');
                    addLogEntry('Binance API: Connected', 'success');
                } else {
                    throw new Error('API returned error status');
                }
            } catch (error) {
                updateAPIStatus('binance', 'error', 'Failed');
                addLogEntry(`Binance API: Failed - ${error.message}`, 'error');
            }
            
            // Test cache
            if (window.realDataIntegration && window.realDataIntegration.cache) {
                updateAPIStatus('cache', 'success', 'Active');
                addLogEntry('Data cache: Active', 'success');
            } else {
                updateAPIStatus('cache', 'warning', 'Not Ready');
                addLogEntry('Data cache: Not ready', 'warning');
            }
        }
        
        function refreshAllData() {
            addLogEntry('Refreshing all data...', 'info');
            
            if (window.realDataIntegration) {
                // Clear cache and reload data
                window.realDataIntegration.cache.clear();
                window.realDataIntegration.loadInitialData().then(() => {
                    addLogEntry('Data refresh completed', 'success');
                }).catch(error => {
                    addLogEntry(`Data refresh failed: ${error.message}`, 'error');
                });
            }
            
            testAPIs();
        }
        
        // Initialize test page
        document.addEventListener('DOMContentLoaded', () => {
            addLogEntry('Test page loaded', 'success');
            
            // Wait for real data integration to load
            setTimeout(() => {
                if (window.realDataIntegration) {
                    addLogEntry('Real data integration loaded', 'success');
                    testAPIs();
                } else {
                    addLogEntry('Real data integration not found', 'error');
                }
            }, 1000);
            
            // Auto-refresh every 30 seconds
            setInterval(() => {
                if (window.realDataIntegration) {
                    addLogEntry('Auto-refresh triggered', 'info');
                    window.realDataIntegration.loadInitialData();
                }
            }, 30000);
        });
    </script>
</body>
</html>
