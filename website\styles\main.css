/* Kontour Coin Main Styles - Purple Gem Theme */

:root {
    --primary-purple: #8B5CF6;
    --secondary-purple: #A855F7;
    --light-purple: #C084FC;
    --lighter-purple: #DDD6FE;
    --dark-purple: #7C3AED;
    --darker-purple: #6D28D9;
    --accent-purple: #F3F4F6;
    --text-dark: #1F2937;
    --text-light: #6B7280;
    --white: #FFFFFF;
    --black: #000000;
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --gradient-primary: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--light-purple) 0%, var(--lighter-purple) 100%);
    --shadow-sm: 0 1px 2px 0 rgba(139, 92, 246, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(139, 92, 246, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(139, 92, 246, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(139, 92, 246, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    padding-top: 80px; /* Account for fixed navbar */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: rgba(139, 92, 246, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 9999;
    padding: 1rem 0;
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin: 0;
    transform: translateZ(0);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--lighter-purple);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--lighter-purple);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--white);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content {
    color: var(--white);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.gradient-text {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--lighter-purple);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.card-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.agent-network {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    align-items: center;
    justify-items: center;
}

.agent-node {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.25rem;
    position: relative;
    transition: all 0.3s ease;
}

.agent-node.coordinator {
    background: var(--gradient-primary);
    grid-column: 2;
}

.agent-node.specialist {
    background: linear-gradient(135deg, var(--secondary-purple), var(--light-purple));
}

.agent-node.validator {
    background: linear-gradient(135deg, var(--light-purple), var(--lighter-purple));
}

.agent-node.optimizer {
    background: linear-gradient(135deg, var(--dark-purple), var(--primary-purple));
}

.agent-node.learner {
    background: linear-gradient(135deg, var(--darker-purple), var(--dark-purple));
}

.agent-node.active {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
    }
}

/* Features Section */
.features {
    padding: 80px 0;
    background: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(139, 92, 246, 0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-purple);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
}

.feature-list li {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li::before {
    content: '💎';
    position: absolute;
    left: 0;
    top: 0;
}

/* Dashboard Section */
.dashboard-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.dashboard-preview {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.dashboard-nav {
    display: flex;
    background: var(--gradient-primary);
    padding: 0;
}

.tab-btn {
    flex: 1;
    padding: 1rem 2rem;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.dashboard-content {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--gradient-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    color: var(--white);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.metric-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Services Status */
.services-status {
    padding: 80px 0;
    background: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.service-title {
    font-weight: 600;
    color: var(--text-dark);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-running {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.status-starting {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.status-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--lighter-purple);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--lighter-purple);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
/* Market Data Cards */
.market-data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.market-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
    transition: all 0.3s ease;
}

.market-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-purple);
}

.coin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.coin-info {
    display: flex;
    flex-direction: column;
}

.coin-name {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.coin-symbol {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

.coin-logo {
    font-size: 2rem;
    opacity: 0.7;
}

.coin-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.coin-change {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.coin-change.positive {
    color: var(--success);
}

.coin-change.negative {
    color: var(--error);
}

.coin-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

.stat-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
}

.market-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.market-header h3 {
    color: var(--text-dark);
    font-size: 1.5rem;
    font-weight: 600;
}

/* Global Stats Section */
.global-stats-section {
    margin: 2rem 0;
    padding: 2rem;
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.global-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.global-stat-card {
    background: var(--gradient-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    color: var(--white);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.global-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.global-stat-card .stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.global-stat-card .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.global-stat-card .stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Trending Section */
.trending-section {
    margin: 2rem 0;
    padding: 2rem;
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.trending-coins {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.trending-coin {
    background: rgba(139, 92, 246, 0.05);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.trending-coin:hover {
    background: rgba(139, 92, 246, 0.1);
    transform: translateY(-1px);
}

.trending-coin .coin-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
}

.trending-coin .coin-info {
    flex: 1;
}

.trending-coin .coin-name {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
    display: block;
}

.trending-coin .coin-symbol {
    font-size: 0.75rem;
    color: var(--text-light);
}

.trending-coin .coin-rank {
    font-size: 0.75rem;
    color: var(--text-light);
    font-weight: 500;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h3 {
    color: var(--text-dark);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .market-data-grid {
        grid-template-columns: 1fr;
    }

    .market-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}
