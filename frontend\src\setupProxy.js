const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Backend API proxy (port 3001)
  app.use(
    '/api/backend',
    createProxyMiddleware({
      target: 'http://localhost:3001',
      changeOrigin: true,
      pathRewrite: {
        '^/api/backend': '/api'
      },
      onError: (err, req, res) => {
        console.log('Backend proxy error:', err.message);
        res.status(503).json({
          error: 'Backend service unavailable',
          message: 'The backend service is currently unavailable. Please try again later.',
          service: 'backend',
          port: 3001
        });
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying ${req.method} ${req.path} to backend`);
      }
    })
  );

  // Wallet service proxy (port 3002) - with fallback
  app.use(
    '/api/wallet',
    createProxyMiddleware({
      target: 'http://localhost:3002',
      changeOrigin: true,
      onError: (err, req, res) => {
        console.log('Wallet service proxy error:', err.message);
        // Provide fallback response for wallet service
        if (req.path.includes('/api/wallet/create')) {
          res.status(503).json({
            error: 'Wallet service unavailable',
            message: 'Wallet service is starting up. Please try again in a moment.',
            fallback: true,
            service: 'wallet',
            port: 3002
          });
        } else if (req.path.includes('/api/tokens/supported')) {
          res.status(503).json({
            error: 'Token service unavailable',
            message: 'Token service is starting up.',
            fallback: true,
            tokens: []
          });
        } else if (req.path.includes('/api/defi/pools')) {
          res.status(503).json({
            error: 'DeFi service unavailable',
            message: 'DeFi service is starting up.',
            fallback: true,
            pools: []
          });
        } else {
          res.status(503).json({
            error: 'Wallet service unavailable',
            message: 'The wallet service is currently unavailable.',
            service: 'wallet',
            port: 3002
          });
        }
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying ${req.method} ${req.path} to wallet service`);
      }
    })
  );

  // AI Agent service proxy (port 8070) - with fallback
  app.use(
    '/api/ai',
    createProxyMiddleware({
      target: 'http://localhost:8070',
      changeOrigin: true,
      onError: (err, req, res) => {
        console.log('AI service proxy error:', err.message);
        res.status(503).json({
          error: 'AI service unavailable',
          message: 'AI agent service is starting up. Please try again later.',
          service: 'ai-agent',
          port: 8070
        });
      }
    })
  );

  // Quantum computing service proxy (port 8030) - with fallback
  app.use(
    '/api/quantum',
    createProxyMiddleware({
      target: 'http://localhost:8030',
      changeOrigin: true,
      onError: (err, req, res) => {
        console.log('Quantum service proxy error:', err.message);
        res.status(503).json({
          error: 'Quantum service unavailable',
          message: 'Quantum computing service is starting up.',
          service: 'quantum',
          port: 8030
        });
      }
    })
  );

  // Web3 workflow service proxy (port 8080) - with fallback
  app.use(
    '/api/web3',
    createProxyMiddleware({
      target: 'http://localhost:8080',
      changeOrigin: true,
      onError: (err, req, res) => {
        console.log('Web3 service proxy error:', err.message);
        res.status(503).json({
          error: 'Web3 service unavailable',
          message: 'Web3 workflow service is starting up.',
          service: 'web3-workflow',
          port: 8080
        });
      }
    })
  );

  // Static files proxy for scripts - with fallback
  app.use(
    '/scripts',
    createProxyMiddleware({
      target: 'http://localhost:3002',
      changeOrigin: true,
      onError: (err, req, res) => {
        console.log('Scripts proxy error:', err.message);
        // Provide fallback JavaScript content
        if (req.path.includes('llm-chat.js')) {
          res.setHeader('Content-Type', 'application/javascript');
          res.status(200).send(`
            // LLM Chat fallback
            console.log('LLM Chat service is starting up...');
            window.LLMChat = {
              init: function() {
                console.log('LLM Chat initialized in fallback mode');
              },
              send: function(message) {
                console.log('LLM Chat service unavailable:', message);
                return Promise.resolve({ error: 'Service unavailable' });
              }
            };
          `);
        } else if (req.path.includes('exchange-integration.js')) {
          res.setHeader('Content-Type', 'application/javascript');
          res.status(200).send(`
            // Exchange Integration fallback
            console.log('Exchange integration service is starting up...');
            window.ExchangeIntegration = {
              init: function() {
                console.log('Exchange integration initialized in fallback mode');
              },
              connect: function() {
                console.log('Exchange service unavailable');
                return Promise.resolve({ error: 'Service unavailable' });
              }
            };
          `);
        } else {
          res.status(404).send('Script not found');
        }
      }
    })
  );

  // Service worker proxy - with fallback
  app.use(
    '/sw.js',
    createProxyMiddleware({
      target: 'http://localhost:3002',
      changeOrigin: true,
      onError: (err, req, res) => {
        console.log('Service worker proxy error:', err.message);
        res.setHeader('Content-Type', 'application/javascript');
        res.status(200).send(`
          // Service Worker fallback
          console.log('Service worker is in fallback mode');
          self.addEventListener('install', function(event) {
            console.log('Service worker installed (fallback)');
          });
          self.addEventListener('fetch', function(event) {
            // Pass through all requests in fallback mode
          });
        `);
      }
    })
  );

  // Favicon proxy - with fallback
  app.use(
    '/favicon.ico',
    createProxyMiddleware({
      target: 'http://localhost:3002',
      changeOrigin: true,
      onError: (err, req, res) => {
        console.log('Favicon proxy error:', err.message);
        // Redirect to a default favicon or return 404
        res.status(404).send('Favicon not found');
      }
    })
  );

  console.log('Proxy middleware configured with error handling and fallbacks');
};
