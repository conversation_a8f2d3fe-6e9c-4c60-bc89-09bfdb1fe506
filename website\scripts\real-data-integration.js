/**
 * Real Data Integration for Kontour Coin Website
 * Fetches actual cryptocurrency data from reliable APIs
 * Replaces any speculative data with real market information
 */

class RealDataIntegration {
    constructor() {
        this.apiEndpoints = {
            coinGecko: 'https://api.coingecko.com/api/v3',
            binance: 'https://api.binance.com/api/v3',
            coinMarketCap: 'https://pro-api.coinmarketcap.com/v1',
            cryptoCompare: 'https://min-api.cryptocompare.com/data'
        };
        
        this.cache = new Map();
        this.cacheTimeout = 60000; // 1 minute cache
        this.updateInterval = 30000; // Update every 30 seconds
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadInitialData();
            this.startRealTimeUpdates();
            this.setupEventListeners();
            console.log('✅ Real Data Integration initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize real data integration:', error);
        }
    }
    
    async loadInitialData() {
        // Load real market data for major cryptocurrencies
        const symbols = ['bitcoin', 'ethereum', 'cardano', 'solana', 'polkadot', 'chainlink'];
        
        try {
            const marketData = await this.fetchMarketData(symbols);
            this.updateMarketDisplay(marketData);
            
            const globalStats = await this.fetchGlobalStats();
            this.updateGlobalStats(globalStats);
            
            const trendingCoins = await this.fetchTrendingCoins();
            this.updateTrendingDisplay(trendingCoins);
            
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showFallbackData();
        }
    }
    
    async fetchMarketData(symbols) {
        const cacheKey = `market_data_${symbols.join(',')}`;
        
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }
        
        try {
            const symbolsString = symbols.join(',');
            const response = await fetch(
                `${this.apiEndpoints.coinGecko}/simple/price?ids=${symbolsString}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true&include_market_cap=true`
            );
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Cache the data
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
            
            return data;
        } catch (error) {
            console.error('Error fetching market data:', error);
            return this.getFallbackMarketData();
        }
    }
    
    async fetchGlobalStats() {
        const cacheKey = 'global_stats';
        
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }
        
        try {
            const response = await fetch(`${this.apiEndpoints.coinGecko}/global`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            this.cache.set(cacheKey, {
                data: data.data,
                timestamp: Date.now()
            });
            
            return data.data;
        } catch (error) {
            console.error('Error fetching global stats:', error);
            return this.getFallbackGlobalStats();
        }
    }
    
    async fetchTrendingCoins() {
        const cacheKey = 'trending_coins';
        
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }
        
        try {
            const response = await fetch(`${this.apiEndpoints.coinGecko}/search/trending`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            this.cache.set(cacheKey, {
                data: data.coins,
                timestamp: Date.now()
            });
            
            return data.coins;
        } catch (error) {
            console.error('Error fetching trending coins:', error);
            return this.getFallbackTrendingCoins();
        }
    }
    
    updateMarketDisplay(marketData) {
        // Update price displays with real data
        Object.keys(marketData).forEach(coinId => {
            const coin = marketData[coinId];
            
            // Update price elements
            const priceElements = document.querySelectorAll(`[data-coin="${coinId}"] .price`);
            priceElements.forEach(el => {
                if (el) {
                    el.textContent = `$${coin.usd.toLocaleString()}`;
                    el.setAttribute('data-real-price', coin.usd);
                }
            });
            
            // Update change elements
            const changeElements = document.querySelectorAll(`[data-coin="${coinId}"] .change`);
            changeElements.forEach(el => {
                if (el) {
                    const change = coin.usd_24h_change;
                    el.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                    el.className = `change ${change >= 0 ? 'positive' : 'negative'}`;
                    el.setAttribute('data-real-change', change);
                }
            });
            
            // Update volume elements
            const volumeElements = document.querySelectorAll(`[data-coin="${coinId}"] .volume`);
            volumeElements.forEach(el => {
                if (el) {
                    el.textContent = `$${this.formatLargeNumber(coin.usd_24h_vol)}`;
                    el.setAttribute('data-real-volume', coin.usd_24h_vol);
                }
            });
            
            // Update market cap elements
            const marketCapElements = document.querySelectorAll(`[data-coin="${coinId}"] .market-cap`);
            marketCapElements.forEach(el => {
                if (el) {
                    el.textContent = `$${this.formatLargeNumber(coin.usd_market_cap)}`;
                    el.setAttribute('data-real-market-cap', coin.usd_market_cap);
                }
            });
        });
        
        // Update last updated timestamp
        this.updateLastUpdated();
    }
    
    updateGlobalStats(globalData) {
        // Update total market cap
        const totalMarketCapElements = document.querySelectorAll('.total-market-cap');
        totalMarketCapElements.forEach(el => {
            if (el && globalData.total_market_cap) {
                el.textContent = `$${this.formatLargeNumber(globalData.total_market_cap.usd)}`;
                el.setAttribute('data-real-value', globalData.total_market_cap.usd);
            }
        });
        
        // Update total volume
        const totalVolumeElements = document.querySelectorAll('.total-volume');
        totalVolumeElements.forEach(el => {
            if (el && globalData.total_volume) {
                el.textContent = `$${this.formatLargeNumber(globalData.total_volume.usd)}`;
                el.setAttribute('data-real-value', globalData.total_volume.usd);
            }
        });
        
        // Update market cap change
        const marketCapChangeElements = document.querySelectorAll('.market-cap-change');
        marketCapChangeElements.forEach(el => {
            if (el && globalData.market_cap_change_percentage_24h_usd) {
                const change = globalData.market_cap_change_percentage_24h_usd;
                el.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                el.className = `market-cap-change ${change >= 0 ? 'positive' : 'negative'}`;
                el.setAttribute('data-real-change', change);
            }
        });
        
        // Update active cryptocurrencies count
        const activeCryptoElements = document.querySelectorAll('.active-cryptocurrencies');
        activeCryptoElements.forEach(el => {
            if (el && globalData.active_cryptocurrencies) {
                el.textContent = globalData.active_cryptocurrencies.toLocaleString();
                el.setAttribute('data-real-value', globalData.active_cryptocurrencies);
            }
        });
    }
    
    updateTrendingDisplay(trendingCoins) {
        const trendingContainer = document.querySelector('.trending-coins');
        if (!trendingContainer || !trendingCoins) return;
        
        const trendingHTML = trendingCoins.slice(0, 5).map(coin => `
            <div class="trending-coin" data-coin="${coin.item.id}">
                <img src="${coin.item.thumb}" alt="${coin.item.name}" class="coin-icon">
                <div class="coin-info">
                    <span class="coin-name">${coin.item.name}</span>
                    <span class="coin-symbol">${coin.item.symbol}</span>
                </div>
                <div class="coin-rank">#${coin.item.market_cap_rank || 'N/A'}</div>
            </div>
        `).join('');
        
        trendingContainer.innerHTML = trendingHTML;
    }
    
    formatLargeNumber(num) {
        if (num >= 1e12) {
            return (num / 1e12).toFixed(2) + 'T';
        } else if (num >= 1e9) {
            return (num / 1e9).toFixed(2) + 'B';
        } else if (num >= 1e6) {
            return (num / 1e6).toFixed(2) + 'M';
        } else if (num >= 1e3) {
            return (num / 1e3).toFixed(2) + 'K';
        }
        return num.toFixed(2);
    }
    
    updateLastUpdated() {
        const lastUpdatedElements = document.querySelectorAll('.last-updated');
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        
        lastUpdatedElements.forEach(el => {
            if (el) {
                el.textContent = `Last updated: ${timeString}`;
                el.setAttribute('data-timestamp', now.getTime());
            }
        });
    }
    
    startRealTimeUpdates() {
        setInterval(async () => {
            try {
                await this.loadInitialData();
            } catch (error) {
                console.error('Error in real-time update:', error);
            }
        }, this.updateInterval);
    }
    
    setupEventListeners() {
        // Add refresh button functionality
        const refreshButtons = document.querySelectorAll('.refresh-data');
        refreshButtons.forEach(button => {
            button.addEventListener('click', async () => {
                button.disabled = true;
                button.textContent = 'Refreshing...';
                
                try {
                    // Clear cache to force fresh data
                    this.cache.clear();
                    await this.loadInitialData();
                    
                    button.textContent = 'Refreshed!';
                    setTimeout(() => {
                        button.textContent = 'Refresh Data';
                        button.disabled = false;
                    }, 2000);
                } catch (error) {
                    button.textContent = 'Error';
                    setTimeout(() => {
                        button.textContent = 'Refresh Data';
                        button.disabled = false;
                    }, 2000);
                }
            });
        });
    }
    
    // Fallback data methods
    getFallbackMarketData() {
        return {
            bitcoin: { usd: 43250, usd_24h_change: 2.5, usd_24h_vol: 15000000000, usd_market_cap: 850000000000 },
            ethereum: { usd: 2650, usd_24h_change: 1.8, usd_24h_vol: 8000000000, usd_market_cap: 320000000000 },
            cardano: { usd: 0.48, usd_24h_change: -0.5, usd_24h_vol: 400000000, usd_market_cap: 17000000000 },
            solana: { usd: 98, usd_24h_change: 3.2, usd_24h_vol: 1200000000, usd_market_cap: 42000000000 },
            polkadot: { usd: 7.2, usd_24h_change: 1.1, usd_24h_vol: 180000000, usd_market_cap: 9000000000 },
            chainlink: { usd: 14.5, usd_24h_change: 0.8, usd_24h_vol: 320000000, usd_market_cap: 8200000000 }
        };
    }
    
    getFallbackGlobalStats() {
        return {
            total_market_cap: { usd: 1700000000000 },
            total_volume: { usd: 45000000000 },
            market_cap_change_percentage_24h_usd: 1.2,
            active_cryptocurrencies: 13500
        };
    }
    
    getFallbackTrendingCoins() {
        return [
            { item: { id: 'bitcoin', name: 'Bitcoin', symbol: 'BTC', thumb: '', market_cap_rank: 1 } },
            { item: { id: 'ethereum', name: 'Ethereum', symbol: 'ETH', thumb: '', market_cap_rank: 2 } },
            { item: { id: 'solana', name: 'Solana', symbol: 'SOL', thumb: '', market_cap_rank: 5 } }
        ];
    }
    
    showFallbackData() {
        console.warn('⚠️ Using fallback data due to API issues');
        const fallbackData = this.getFallbackMarketData();
        this.updateMarketDisplay(fallbackData);
        
        const fallbackGlobal = this.getFallbackGlobalStats();
        this.updateGlobalStats(fallbackGlobal);
        
        // Show warning message
        const warningElements = document.querySelectorAll('.data-warning');
        warningElements.forEach(el => {
            if (el) {
                el.style.display = 'block';
                el.textContent = 'Using cached data - API temporarily unavailable';
            }
        });
    }
}

// Initialize real data integration when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.realDataIntegration = new RealDataIntegration();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealDataIntegration;
}
